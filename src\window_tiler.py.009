#!/usr/bin/env python3
"""
Refactored Window Tiler (Now Includes Explorer.exe, With Extra "Real Window" Checks)

Key points:
- Enumerates top-level "Alt+Tab style" windows (including minimized),
  but adds extra checks so only truly "real" application windows appear.
- Groups them by process name OR by a simple "window type".
- Lets you pick which group to tile.
- Optionally tries to unminimize windows (SW_SHOWNORMAL + SetWindowPos).
- Uses class-based architecture for clearer design.

Requirements:
    pip install pywin32 psutil
"""

import os
import sys
import ctypes
from ctypes import wintypes
import psutil

import win32api
import win32gui
import win32con
import win32process

from enum import Enum, auto

# --------------------------------------------------------------------------------
# 0) Extra Cloaking Detection (for Windows 8+ / 10+)
# --------------------------------------------------------------------------------
# This helps skip "cloaked" windows, which are never actually visible to the user.

try:
    dwmapi = ctypes.WinDLL("dwmapi", use_last_error=True)
    DWMWA_CLOAKED = 14

    def is_window_cloaked(hwnd):
        """Return True if the window is cloaked (not actually visible), else False."""
        cloaked = wintypes.INT(0)
        res = dwmapi.DwmGetWindowAttribute(
            hwnd,
            DWMWA_CLOAKED,
            ctypes.byref(cloaked),
            ctypes.sizeof(cloaked)
        )
        if res == 0 and cloaked.value != 0:
            return True
        return False
except OSError:
    # If DWM APIs aren't available (older OS?), fallback to a dummy function
    def is_window_cloaked(hwnd):
        return False


# --------------------------------------------------------------------------------
# Global Helper (Needed for older PyWin32 if GetShellWindow doesn't exist)
# --------------------------------------------------------------------------------
user32 = ctypes.WinDLL("user32", use_last_error=True)
GetShellWindow = user32.GetShellWindow
GetShellWindow.restype = wintypes.HWND
SHELL_WINDOW = GetShellWindow()

# ---------------------------------------------------------
# Existing Enumeration
# ---------------------------------------------------------
class WindowType(Enum):
    BROWSER = auto()
    TERMINAL = auto()
    EXPLORER = auto()
    EDITOR = auto()
    IDE = auto()
    NORMAL = auto()
    UNKNOWN = auto()

# ---------------------------------------------------------
# Existing Window, Monitor Classes
# ---------------------------------------------------------
class Window:
    """
    Represents a single top-level window.
    Relies on external detection/heuristics but offers basic manipulation.
    """
    def __init__(self, hwnd, exe_path, rect):
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.class_name = win32gui.GetClassName(hwnd)
        self.exe_path = exe_path  # full path from psutil
        self.process_name = os.path.basename(exe_path).lower() if exe_path else ""
        self.window_type = WindowType.UNKNOWN

        # rect: (left, top, right, bottom) from window placement
        self.rect = rect
        self._classify()

    def _classify(self):
        """Rough classification by process name."""
        pname = self.process_name
        cname = self.class_name.lower()

        if any(b in pname for b in ["chrome.exe", "msedge.exe", "firefox.exe", "iexplore.exe"]):
            self.window_type = WindowType.BROWSER
        elif any(t in pname for t in ["cmd.exe", "powershell.exe", "windowsterminal.exe"]):
            self.window_type = WindowType.TERMINAL
        elif "explorer.exe" in pname or "cabinetwclass" in cname:
            self.window_type = WindowType.EXPLORER
        elif any(e in pname for e in ["notepad.exe", "code.exe", "sublime_text.exe"]):
            self.window_type = WindowType.EDITOR
        elif any(i in pname for i in ["devenv.exe", "pycharm", "idea64"]):
            self.window_type = WindowType.IDE
        else:
            self.window_type = WindowType.NORMAL

    def move_and_resize(self, x, y, width, height, restore_minimized=True):
        """Position and optionally restore the window."""
        if restore_minimized and win32gui.IsIconic(self.hwnd):
            # SW_SHOWNORMAL is more reliable than SW_RESTORE for typical apps
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOWNORMAL)

        # Move it
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

        # Bring to top without stealing focus
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            x, y, width, height,
            win32con.SWP_NOSENDCHANGING | win32con.SWP_SHOWWINDOW
        )

    def __repr__(self):
        return f"<Window hwnd={self.hwnd} title='{self.title}' exe='{self.exe_path}' type={self.window_type.name}>"


class Monitor:
    """Represents a physical display monitor."""
    def __init__(self, handle, info):
        self.handle = handle
        self.is_primary = bool(info["Flags"] == 1)
        self.device = info["Device"]
        mon_rect = info["Monitor"]
        work_rect = info["Work"]
        self.monitor_area = mon_rect
        self.work_area = work_rect

    def get_dimensions(self):
        l, t, r, b = self.monitor_area
        return {"width": r - l, "height": b - t}


# ---------------------------------------------------------
# 1) WindowManager
# ---------------------------------------------------------
class WindowManager:
    """
    Responsible for discovering and managing windows,
    *very strictly* filtering only "Alt+Tab style" user-facing windows.
    """
    def __init__(self):
        self.windows = []

    def detect_windows(self, min_size=2, allow_minimized=True):
        """
        Enumerate windows that appear in an actual user-facing Alt+Tab scenario
        and pass extra checks. We optionally include minimized windows
        if allow_minimized=True (so we can un-minimize them).
        """
        self.windows = []

        def enum_callback(hwnd, _):
            if not self.is_really_an_alt_tab_window(hwnd, allow_minimized):
                return True

            # Attempt process info
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            try:
                proc = psutil.Process(pid)
                exe_path = proc.exe().lower()
            except (psutil.Error, psutil.NoSuchProcess):
                return True

            # Check geometry
            placement = win32gui.GetWindowPlacement(hwnd)
            left, top, right, bottom = placement[4]
            width = right - left
            height = bottom - top

            if width < min_size or height < min_size:
                return True

            # Keep it
            self.windows.append(Window(hwnd, exe_path, (left, top, right, bottom)))
            return True

        win32gui.EnumWindows(enum_callback, None)

    def get_windows_by_process(self):
        """Group windows by process name."""
        groups = {}
        for w in self.windows:
            pname = w.process_name or "unknown"
            groups.setdefault(pname, []).append(w)
        return groups

    def get_windows_by_type(self):
        """Group windows by WindowType."""
        groups = {}
        for w in self.windows:
            groups.setdefault(w.window_type, []).append(w)
        return groups

    def filter_windows(self, predicate):
        """Return a list of windows matching the given predicate."""
        return [w for w in self.windows if predicate(w)]

    # ---------------------------------------------------------------------
    # The Core "Is This a Real Alt+Tab Window?" Logic
    # ---------------------------------------------------------------------
    def is_really_an_alt_tab_window(self, hwnd, allow_minimized):
        """
        Return True if hwnd is a "real" user-facing window we'd see in Alt+Tab,
        or at least can be un-minimized (if allow_minimized=True).

        The following checks are used:

        1. Not the Shell Window
        2. Must be root ancestor (no parent)
        3. No WS_EX_TOOLWINDOW
        4. No owner (GW_OWNER)
        5. Non-empty title
        6. Not cloaked (DWM)
        7. Window is enabled
        8. Must have WS_CAPTION or WS_SYSMENU or WS_EX_APPWINDOW
        9. If not allow_minimized, must not be minimized
        """
        # 1) Not the shell
        if hwnd == SHELL_WINDOW:
            return False

        # 2) Root ancestor
        if win32gui.GetAncestor(hwnd, win32con.GA_ROOT) != hwnd:
            return False

        ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
        style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)

        # 3) Skip toolwindows
        if ex_style & win32con.WS_EX_TOOLWINDOW:
            return False

        # 4) No owner
        if win32gui.GetWindow(hwnd, win32con.GW_OWNER) != 0:
            return False

        # 5) Non-empty title
        title = win32gui.GetWindowText(hwnd)
        if not title.strip():
            return False

        # 6) Not cloaked
        if is_window_cloaked(hwnd):
            return False

        # 7) Must be enabled
        if not win32gui.IsWindowEnabled(hwnd):
            return False

        # 8) Must have typical window styles
        #    We check for either WS_CAPTION, WS_SYSMENU, or WS_EX_APPWINDOW
        #    (some windows use WS_POPUP + WS_EX_APPWINDOW to appear in alt+tab).
        has_caption = bool(style & win32con.WS_CAPTION)
        has_sysmenu = bool(style & win32con.WS_SYSMENU)
        has_appwindow = bool(ex_style & win32con.WS_EX_APPWINDOW)
        if not (has_caption or has_sysmenu or has_appwindow):
            return False

        # 9) If we do NOT allow minimized, skip if it's currently minimized
        if not allow_minimized and win32gui.IsIconic(hwnd):
            return False

        return True



# ---------------------------------------------------------
# 2) MonitorManager
# ---------------------------------------------------------
class MonitorManager:
    """Responsible for discovering monitors and retrieving user-chosen monitors."""
    def __init__(self):
        self.monitors = []

    def detect_monitors(self):
        """Populate self.monitors with discovered Monitor objects."""
        self.monitors = []
        for (handle, _, info) in win32api.EnumDisplayMonitors(None, None):
            mon_info = win32api.GetMonitorInfo(handle)
            self.monitors.append(Monitor(handle, mon_info))

    def get_primary_monitor(self):
        """Return the primary monitor, or None if none found."""
        for m in self.monitors:
            if m.is_primary:
                return m
        return self.monitors[0] if self.monitors else None

    def get_monitor_by_index(self, index):
        """Return monitor by list index or None if out of range."""
        if 0 <= index < len(self.monitors):
            return self.monitors[index]
        return None

# ---------------------------------------------------------
# 3) LayoutManager
# ---------------------------------------------------------
class LayoutManager:
    """
    Responsible for applying different layout strategies (grid, custom, etc.).
    """
    def apply_grid_layout(self, windows, monitor, rows=2, cols=2, restore_minimized=True):
        """Simple grid layout on the chosen monitor."""
        if not windows:
            print("No windows to tile.")
            return

        left, top, right, bottom = monitor.monitor_area
        total_width = right - left
        total_height = bottom - top

        n = min(len(windows), rows * cols)

        for i, w in enumerate(windows[:n]):
            row = i // cols
            col = i % cols

            x = left + int(col * (total_width / cols))
            y = top + int(row * (total_height / rows))
            wth = int(total_width / cols)
            hth = int(total_height / rows)

            w.move_and_resize(x, y, wth, hth, restore_minimized=restore_minimized)

        print(f"Tiled {n} windows in a {rows}x{cols} grid on {monitor.device}.")

    def apply_custom_layout(self, windows, monitor, layout_config):
        """Example placeholder for custom layouts."""
        pass

    def create_layout_preset(self, name, config):
        """Register a named preset for future usage."""
        pass

# ---------------------------------------------------------
# 4) UserInterface
# ---------------------------------------------------------
class UserInterface:
    """Handles all user-facing input/output prompts."""
    def show_monitor_selection(self, monitors):
        print("\nMonitors Detected:")
        for i, mon in enumerate(monitors):
            dims = mon.get_dimensions()
            primary_txt = " [PRIMARY]" if mon.is_primary else ""
            print(f"{i}) {mon.device} - {dims['width']}x{dims['height']}{primary_txt}")

    def prompt_monitor_index(self):
        return input("Select a monitor index [blank=choose primary]: ").strip()

    def show_grouped_windows(self, grouped):
        """Display grouped windows (key -> count)."""
        print("\nGroups Detected:")
        group_keys = sorted(grouped.keys(), key=lambda k: str(k))
        for idx, key in enumerate(group_keys):
            label = str(key)
            if isinstance(key, WindowType):
                label = key.name
            print(f"{idx}) {label} -> {len(grouped[key])} windows")
        return group_keys

    def get_user_choice_index(self):
        return input("\nWhich group do you want to tile? Enter index: ").strip()

    def get_layout_configuration(self):
        """Prompt for rows, columns, and whether to restore minimized."""
        r = input("Number of rows [default=2]: ").strip()
        c = input("Number of columns [default=2]: ").strip()
        rows = int(r) if r.isdigit() else 2
        cols = int(c) if c.isdigit() else 2

        restore_prompt = input("Restore minimized windows? [Y/n]: ").strip().lower()
        restore_minimized = (restore_prompt != 'n')
        return rows, cols, restore_minimized

    def display_results(self, message):
        print(message)

# ---------------------------------------------------------
# 5) WindowTilerApp
# ---------------------------------------------------------
class WindowTilerApp:
    """
    Main application class that composes WindowManager, MonitorManager,
    LayoutManager, and UserInterface. Orchestrates the flow.
    """
    def __init__(self):
        self.windowManager = WindowManager()
        self.monitorManager = MonitorManager()
        self.layoutManager = LayoutManager()
        self.ui = UserInterface()
        self.running = True

    def run(self):
        """Main entry point of the application."""
        print("Gathering top-level windows (including minimized).")
        self.windowManager.detect_windows(min_size=2)
        windows = self.windowManager.windows

        if not windows:
            self.ui.display_results("No windows found!")
            return

        print(f"Total windows found: {len(windows)}")

        # Step: Choose grouping style
        print("\nChoose grouping style:")
        print("1) By process name (e.g. 'chrome.exe')")
        print("2) By window type (BROWSER, TERMINAL, EDITOR, etc.)")
        choice = input("Enter [1/2]: ").strip()

        if choice == "1":
            grouped = self.windowManager.get_windows_by_process()
        else:
            grouped = self.windowManager.get_windows_by_type()

        # Display group info
        group_keys = self.ui.show_grouped_windows(grouped)
        if not group_keys:
            self.ui.display_results("No groups found!")
            return

        # Step: Pick group
        chosen = self.ui.get_user_choice_index()
        try:
            chosen_idx = int(chosen)
            group_key = group_keys[chosen_idx]
        except:
            self.ui.display_results("Invalid choice, quitting.")
            return

        # Step: Layout config
        rows, cols, restore_minimized = self.ui.get_layout_configuration()

        # Step: Monitor selection
        self.monitorManager.detect_monitors()
        monitors = self.monitorManager.monitors
        if not monitors:
            self.ui.display_results("No monitors found!")
            return

        self.ui.show_monitor_selection(monitors)
        index_str = self.ui.prompt_monitor_index()

        if not index_str:
            monitor = self.monitorManager.get_primary_monitor()
        else:
            try:
                idx = int(index_str)
                monitor = self.monitorManager.get_monitor_by_index(idx)
            except:
                self.ui.display_results("Invalid input, defaulting to first monitor.")
                monitor = monitors[0]

        if not monitor:
            monitor = monitors[0]

        # Step: Do the layout
        windows_to_tile = grouped[group_key]
        self.layoutManager.apply_grid_layout(
            windows_to_tile,
            monitor,
            rows,
            cols,
            restore_minimized=restore_minimized
        )

        self.ui.display_results("\nDone.")

    def exit(self):
        """Clean up resources if needed and exit."""
        self.running = False

# ---------------------------------------------------------
# 6) Entry Point
# ---------------------------------------------------------
def main():
    app = WindowTilerApp()
    app.run()

if __name__ == "__main__":
    main()
