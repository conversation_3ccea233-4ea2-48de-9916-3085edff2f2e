#!/usr/bin/env python3
"""
Enhanced Window Tiler with optional minimized-window restoration,
integrating style-based (Alt+Tab) filtering with psutil-based process checks
and dimension checks, all in one script.

Requirements:
    pip install pywin32 psutil
"""

import os
import sys
import ctypes
from ctypes import wintypes

import psutil
import win32api
import win32gui
import win32con
import win32process

from enum import Enum, auto

# -------------------------------------------------------------------------
# A) Retrieve Shell Window via ctypes (for older PyWin32)
# -------------------------------------------------------------------------
user32 = ctypes.WinDLL("user32", use_last_error=True)
GetShellWindow = user32.GetShellWindow
GetShellWindow.restype = wintypes.HWND

SHELL_WINDOW = GetShellWindow()

# -------------------------------------------------------------------------
# 1) Enumerations & Basic Classes
# -------------------------------------------------------------------------
class WindowType(Enum):
    BROWSER = auto()
    TERMINAL = auto()
    EXPLORER = auto()
    EDITOR = auto()
    IDE = auto()
    NORMAL = auto()
    UNKNOWN = auto()

class Monitor:
    """Represents a physical display monitor."""
    def __init__(self, handle, info):
        self.handle = handle
        self.is_primary = bool(info["Flags"] == 1)
        self.device = info["Device"]
        # Monitor area (left, top, right, bottom)
        mon_rect = info["Monitor"]
        work_rect = info["Work"]
        self.monitor_area = mon_rect
        self.work_area = work_rect

    def get_dimensions(self):
        left, top, right, bottom = self.monitor_area
        return {"width": right - left, "height": bottom - top}


class Window:
    """
    Represents a single top-level window that passes our style-based filters.
    We retrieve process info via psutil for skipping certain exe's if desired.
    """
    def __init__(self, hwnd, exe_path, rect):
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.class_name = win32gui.GetClassName(hwnd)
        self.exe_path = exe_path  # full path from psutil
        self.process_name = os.path.basename(exe_path).lower() if exe_path else ""
        self.window_type = WindowType.UNKNOWN

        self.rect = rect  # (left, top, right, bottom)

        self._classify()

    def _classify(self):
        pname = self.process_name
        cname = self.class_name.lower()

        if any(b in pname for b in ["chrome.exe", "msedge.exe", "firefox.exe", "iexplore.exe"]):
            self.window_type = WindowType.BROWSER
        elif any(t in pname for t in ["cmd.exe", "powershell.exe", "windowsterminal.exe"]):
            self.window_type = WindowType.TERMINAL
        elif "explorer.exe" in pname or "cabinetwclass" in cname:
            self.window_type = WindowType.EXPLORER
        elif any(e in pname for e in ["notepad.exe", "code.exe", "sublime_text.exe"]):
            self.window_type = WindowType.EDITOR
        elif any(i in pname for i in ["devenv.exe", "pycharm", "idea64"]):
            self.window_type = WindowType.IDE
        else:
            self.window_type = WindowType.NORMAL

    def move_and_resize(self, x, y, width, height, restore_minimized=True):
        if restore_minimized and win32gui.IsIconic(self.hwnd):
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOWNORMAL)

        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            x, y, width, height,
            win32con.SWP_NOSENDCHANGING | win32con.SWP_SHOWWINDOW
        )

    def __repr__(self):
        return (f"<Window hwnd={self.hwnd} title='{self.title}' "
                f"exe='{self.exe_path}' type={self.window_type.name}>")


# -------------------------------------------------------------------------
# 2) Alt+Tab Style Filter + psutil + dimension checks
# -------------------------------------------------------------------------
def is_alt_tab_style_window(hwnd):
    """
    True if this window is a normal user-facing (Alt+Tab) top-level window.
    """
    # Compare to the shell window from ctypes approach
    if hwnd == SHELL_WINDOW:
        return False

    # Must be root ancestor
    if win32gui.GetAncestor(hwnd, win32con.GA_ROOT) != hwnd:
        return False

    ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
    if ex_style & win32con.WS_EX_TOOLWINDOW:
        return False

    if win32gui.GetWindow(hwnd, win32con.GW_OWNER) != 0:
        return False

    title = win32gui.GetWindowText(hwnd)
    if not title.strip():
        return False

    return True


def get_all_windows(skip_explorer=True, min_size=2):
    """
    Enumerate "Alt+Tab style" top-level windows, retrieve process exe path via psutil,
    optionally skip explorer.exe, and skip windows smaller than min_size.

    Returns list of Window objects.
    """
    results = []

    def enum_callback(hwnd, _):
        if not is_alt_tab_style_window(hwnd):
            return True

        _, pid = win32process.GetWindowThreadProcessId(hwnd)
        try:
            p = psutil.Process(pid)
            exe_path = p.exe().lower()
        except (psutil.Error, psutil.NoSuchProcess):
            return True

        if skip_explorer and "explorer.exe" in exe_path:
            return True

        placement = win32gui.GetWindowPlacement(hwnd)
        left, top, right, bottom = placement[4]
        width = right - left
        height = bottom - top

        if width < min_size or height < min_size:
            return True

        w = Window(hwnd, exe_path, (left, top, right, bottom))
        results.append(w)
        return True

    win32gui.EnumWindows(enum_callback, None)
    return results


# -------------------------------------------------------------------------
# 3) Grouping
# -------------------------------------------------------------------------
def group_by_process_name(windows):
    groups = {}
    for w in windows:
        pname = w.process_name or "unknown"
        groups.setdefault(pname, []).append(w)
    return groups

def group_by_window_type(windows):
    groups = {}
    for w in windows:
        wtype = w.window_type
        groups.setdefault(wtype, []).append(w)
    return groups

# -------------------------------------------------------------------------
# 4) Monitor Handling
# -------------------------------------------------------------------------
class WrappedMonitor:
    def __init__(self, monitor_obj, index):
        self.monitor_obj = monitor_obj
        self.index = index

    def __str__(self):
        dims = self.monitor_obj.get_dimensions()
        p_txt = " [PRIMARY]" if self.monitor_obj.is_primary else ""
        return f"{self.index}) {self.monitor_obj.device} - {dims['width']}x{dims['height']}{p_txt}"

def get_all_monitors():
    monitors = []
    for (handle, _, info) in win32api.EnumDisplayMonitors(None, None):
        mon_info = win32api.GetMonitorInfo(handle)
        monitors.append(Monitor(handle, mon_info))
    return monitors

def choose_monitor():
    monitor_objs = get_all_monitors()
    if not monitor_objs:
        print("No monitors found!")
        sys.exit(1)

    wrapped = [WrappedMonitor(m, i) for i, m in enumerate(monitor_objs)]

    print("\nMonitors Detected:")
    for w in wrapped:
        print(w)

    choice = input("Select a monitor index [blank=choose primary]: ").strip()
    if not choice:
        for m in monitor_objs:
            if m.is_primary:
                return m
        return monitor_objs[0]

    try:
        idx = int(choice)
        if 0 <= idx < len(monitor_objs):
            return monitor_objs[idx]
        else:
            print("Invalid index, defaulting to first.")
            return monitor_objs[0]
    except:
        print("Invalid input, defaulting to first monitor.")
        return monitor_objs[0]

# -------------------------------------------------------------------------
# 5) Tiling
# -------------------------------------------------------------------------
def tile_in_grid(monitor, windows, rows=2, cols=2, restore_minimized=True):
    if not windows:
        print("No windows to tile.")
        return

    left, top, right, bottom = monitor.monitor_area
    tw = right - left
    th = bottom - top

    n = min(len(windows), rows*cols)
    for i, w in enumerate(windows[:n]):
        row = i // cols
        col = i % cols
        x = left + int(col * (tw / cols))
        y = top + int(row * (th / rows))
        wth = int(tw / cols)
        hth = int(th / rows)
        w.move_and_resize(x, y, wth, hth, restore_minimized=restore_minimized)

    print(f"Tiled {n} windows in a {rows}x{cols} grid on {monitor.device}.")

# -------------------------------------------------------------------------
# 6) Main CLI
# -------------------------------------------------------------------------
def main():
    print("Gathering top-level alt-tab windows (including minimized) with psutil checks.")
    windows = get_all_windows(skip_explorer=True, min_size=2)
    if not windows:
        print("No windows found!")
        return

    print(f"Total windows found: {len(windows)}")

    print("\nChoose grouping style:")
    print("1) By process name (e.g. 'chrome.exe')")
    print("2) By window type (BROWSER, TERMINAL, EDITOR, etc.)")
    choice = input("Enter [1/2]: ").strip()

    if choice == "1":
        groups = group_by_process_name(windows)
    else:
        groups = group_by_window_type(windows)

    print("\nGroups Detected:")
    group_keys = sorted(groups.keys(), key=lambda k: str(k))
    for idx, key in enumerate(group_keys):
        label = str(key)
        if isinstance(key, WindowType):
            label = key.name
        print(f"{idx}) {label} -> {len(groups[key])} windows")

    if not group_keys:
        print("No groups found!")
        return

    chosen = input("\nWhich group do you want to tile? Enter index: ").strip()
    try:
        chosen_idx = int(chosen)
        group_key = group_keys[chosen_idx]
    except:
        print("Invalid choice, quitting.")
        return

    r = input("Number of rows [default=2]: ").strip()
    c = input("Number of columns [default=2]: ").strip()
    rows = int(r) if r.isdigit() else 2
    cols = int(c) if c.isdigit() else 2

    restore_prompt = input("Restore minimized windows? [Y/n]: ").strip().lower()
    restore_minimized = (restore_prompt != 'n')

    monitor = choose_monitor()
    tile_in_grid(monitor, groups[group_key], rows, cols, restore_minimized=restore_minimized)

    print("\nDone.")

if __name__ == "__main__":
    main()
